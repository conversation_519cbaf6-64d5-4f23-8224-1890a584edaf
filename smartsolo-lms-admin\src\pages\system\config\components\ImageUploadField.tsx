import React from 'react';
import { Form, Image } from 'antd';
import { UploadImageButton } from '../../../../compenents';

interface ImageUploadFieldProps {
  label: string;
  name: string;
  imageUrl?: string;
  buttonText: string;
  helpText: string;
  onImageSelected: (url: string, id: string) => void;
  imageStyle?: React.CSSProperties;
  labelStyle?: React.CSSProperties;
}

const ImageUploadField: React.FC<ImageUploadFieldProps> = ({
  label,
  name,
  imageUrl,
  buttonText,
  helpText,
  onImageSelected,
  imageStyle = {},
  labelStyle = {},
}) => {
  return (
    <Form.Item
      style={{ marginBottom: 30 }}
      label={label}
      name={name}
      labelCol={imageUrl ? { style: labelStyle } : undefined}
    >
      <div className="d-flex">
        {imageUrl && (
          <Image
            preview={false}
            src={imageUrl}
            style={imageStyle}
          />
        )}
        <div className="d-flex ml-24">
          <UploadImageButton
            text={buttonText}
            onSelected={onImageSelected}
          />
          <div className="helper-text ml-8">
            {helpText}
          </div>
        </div>
      </div>
    </Form.Item>
  );
};

export default ImageUploadField;
