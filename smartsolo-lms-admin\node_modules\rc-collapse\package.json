{"name": "rc-collapse", "version": "3.9.0", "description": "rc-collapse ui component for react", "keywords": ["react", "react-component", "react-rc-collapse", "rc-collapse", "collapse", "accordion"], "homepage": "http://github.com/react-component/collapse", "bugs": {"url": "http://github.com/react-component/collapse/issues"}, "repository": {"type": "git", "url": "**************:react-component/collapse.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "typings": "es/index.d.ts", "files": ["lib", "es", "assets/*.css"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "npm run docs:build && gh-pages -d .doc", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prepare": "husky", "now-build": "npm run docs:build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "postpublish": "npm run docs:deploy", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{ts,tsx,js,jsx,json,md}": "npm run prettier"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.3.4", "rc-util": "^5.27.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/classnames": "^2.2.9", "@types/jest": "^29.4.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^4.0.0", "dumi": "^2.1.1", "eslint": "^8.55.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^49.0.0", "father": "^4.1.3", "husky": "^9.0.0", "jest": "^29.1.2", "less": "^4.2.0", "lint-staged": "^15.0.2", "np": "^9.1.0", "prettier": "^3.0.3", "rc-test": "^7.0.14", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}