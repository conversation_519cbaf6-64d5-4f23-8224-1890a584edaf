{"name": "rc-dialog", "version": "9.6.0", "description": "dialog ui component for react", "keywords": ["react", "react-component", "react-dialog", "dialog", "ui"], "homepage": "http://github.com/react-component/dialog", "bugs": {"url": "http://github.com/react-component/dialog/issues"}, "repository": {"type": "git", "url": "**************:react-component/dialog.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es", "assets/*.css", "dist"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css && lessc assets/bootstrap.less assets/bootstrap.css", "coverage": "rc-test --coverage", "deploy": "npm run docs:build && npm run docs:deploy", "docs:build": "dumi build", "docs:deploy": "gh-pages -d dist", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-8", "classnames": "^2.2.6", "rc-motion": "^2.3.0", "rc-util": "^5.21.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^12.0.0", "@types/enzyme": "^3.10.7", "@types/jest": "^29.4.0", "@types/keyv": "3.1.4", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@umijs/fabric": "^3.0.0", "bootstrap": "^4.3.1", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.0", "dumi": "^2.1.3", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-react": "^7.20.6", "father": "^4.1.5", "gh-pages": "^6.1.1", "glob": "^11.0.0", "husky": "^8.0.3", "less": "^4.1.3", "lint-staged": "^15.2.0", "np": "^10.0.5", "prettier": "^3.2.1", "rc-drawer": "^7.0.0", "rc-select": "^14.11.0", "rc-test": "^7.0.14", "react": "^16.9.0", "react-dom": "^16.9.0", "react-draggable": "^4.4.3", "typescript": "^5.4.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}