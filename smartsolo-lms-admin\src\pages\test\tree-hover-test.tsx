import React, { useState } from 'react';
import { Tree, Tooltip } from 'antd';
import type { TreeDataNode, TreeProps } from 'antd';

const TreeHoverTest: React.FC = () => {
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['0-0', '0-0-0']);

  const treeData: TreeDataNode[] = [
    {
      title: (
        <div className="d-flex">
          <div className="d-flex">
            <i
              className="iconfont icon-icon-video"
              style={{
                fontSize: 16,
                color: "rgba(0,0,0,0.3)",
              }}
            />
            <div className="tree-video-title mr-24">测试视频课程 1</div>
          </div>
          <Tooltip placement="top" title="可拖拽排序">
            <i
              className="iconfont icon-icon-drag mr-16"
              style={{ fontSize: 24 }}
            />
          </Tooltip>
          <i
            className="iconfont icon-icon-delete"
            style={{ fontSize: 24 }}
            onClick={() => console.log('删除')}
          />
        </div>
      ),
      key: '0-0',
      children: [
        {
          title: (
            <div className="d-flex">
              <div className="d-flex">
                <i
                  className="iconfont icon-icon-file"
                  style={{
                    fontSize: 16,
                    color: "rgba(0,0,0,0.3)",
                  }}
                />
                <div className="tree-video-title mr-24">子课程 1-1</div>
              </div>
              <i
                className="iconfont icon-icon-delete"
                style={{ fontSize: 24 }}
                onClick={() => console.log('删除子课程')}
              />
            </div>
          ),
          key: '0-0-0',
        },
        {
          title: (
            <div className="d-flex">
              <div className="d-flex">
                <i
                  className="iconfont icon-icon-file"
                  style={{
                    fontSize: 16,
                    color: "rgba(0,0,0,0.3)",
                  }}
                />
                <div className="tree-video-title mr-24">子课程 1-2</div>
              </div>
              <i
                className="iconfont icon-icon-delete"
                style={{ fontSize: 24 }}
                onClick={() => console.log('删除子课程')}
              />
            </div>
          ),
          key: '0-0-1',
        },
      ],
    },
    {
      title: (
        <div className="d-flex">
          <div className="d-flex">
            <i
              className="iconfont icon-icon-video"
              style={{
                fontSize: 16,
                color: "rgba(0,0,0,0.3)",
              }}
            />
            <div className="tree-video-title mr-24">测试视频课程 2</div>
          </div>
          <Tooltip placement="top" title="可拖拽排序">
            <i
              className="iconfont icon-icon-drag mr-16"
              style={{ fontSize: 24 }}
            />
          </Tooltip>
          <i
            className="iconfont icon-icon-delete"
            style={{ fontSize: 24 }}
            onClick={() => console.log('删除')}
          />
        </div>
      ),
      key: '0-1',
    },
    {
      title: (
        <div className="d-flex">
          <div className="d-flex">
            <i
              className="iconfont icon-icon-video"
              style={{
                fontSize: 16,
                color: "rgba(0,0,0,0.3)",
              }}
            />
            <div className="tree-video-title mr-24">测试视频课程 3</div>
          </div>
          <Tooltip placement="top" title="可拖拽排序">
            <i
              className="iconfont icon-icon-drag mr-16"
              style={{ fontSize: 24 }}
            />
          </Tooltip>
          <i
            className="iconfont icon-icon-delete"
            style={{ fontSize: 24 }}
            onClick={() => console.log('删除')}
          />
        </div>
      ),
      key: '0-2',
    },
  ];

  const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    console.log('selected', selectedKeys, info);
    setSelectedKeys(selectedKeys);
  };

  const onExpand: TreeProps['onExpand'] = (expandedKeys, info) => {
    console.log('expanded', expandedKeys, info);
    setExpandedKeys(expandedKeys);
  };

  return (
    <div style={{ padding: '24px' }}>
      <h2>Tree Hover 抖动测试页面</h2>
      <p>请将鼠标移动到树节点的边缘位置，观察是否还有抖动现象。</p>
      
      <div style={{ width: '500px', border: '1px solid #d9d9d9', padding: '16px' }}>
        <h3>修复后的 Tree 组件：</h3>
        <div className="smartsolo-tree">
          <Tree
            selectedKeys={selectedKeys}
            expandedKeys={expandedKeys}
            onSelect={onSelect}
            onExpand={onExpand}
            treeData={treeData}
            switcherIcon={<i className="iconfont icon-icon-fold c-gray" />}
          />
        </div>
      </div>

      <div style={{ width: '500px', border: '1px solid #d9d9d9', padding: '16px', marginTop: '24px' }}>
        <h3>原始 Tree 组件（对比用）：</h3>
        <Tree
          selectedKeys={selectedKeys}
          expandedKeys={expandedKeys}
          onSelect={onSelect}
          onExpand={onExpand}
          treeData={treeData}
          switcherIcon={<i className="iconfont icon-icon-fold c-gray" />}
        />
      </div>
    </div>
  );
};

export default TreeHoverTest;
