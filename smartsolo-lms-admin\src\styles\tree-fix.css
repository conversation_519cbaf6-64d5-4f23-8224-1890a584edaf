/* Tree 组件防抖动修复样式 */

/* 全局 Tree 容器防抖动 */
.ant-tree {
  /* 防止整个树的抖动 */
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
}

/* 针对 smartsolo-tree 的特殊处理 */
.smartsolo-tree .ant-tree-treenode {
  /* 强制使用 GPU 加速，减少重绘 */
  transform: translateZ(0);
  backface-visibility: hidden;

  /* 防止边缘检测问题 - 增加一些内边距来稳定 hover 区域 */
  margin: 0 2px;
  padding: 0 2px;

  /* 确保 hover 区域稳定 */
  min-height: 40px;
  max-height: 40px;

  /* 防止快速 hover 切换导致的抖动 */
  transition-delay: 0.05s;
}

/* 防止内容包装器的抖动 */
.smartsolo-tree .ant-tree-node-content-wrapper,
.smartsolo-tree .ant-tree-node-content-wrapper-normal {
  /* 禁用所有可能导致抖动的过渡效果 */
  transition: none !important;
  transform: none !important;
  
  /* 防止背景色变化导致的抖动 */
  background: transparent !important;
  
  /* 确保尺寸稳定 */
  box-sizing: border-box;
  
  /* 防止 hover 状态的样式冲突 */
  &:hover {
    background: transparent !important;
    color: inherit !important;
  }
}

/* 防止 switcher 的抖动 */
.smartsolo-tree .ant-tree-switcher {
  /* 固定尺寸，防止布局变化 */
  min-width: 24px;
  max-width: 24px;
  flex-shrink: 0;
  
  /* 防止图标变化导致的抖动 */
  &::before {
    transition: none !important;
  }
}

/* 防止 title 区域的抖动 */
.smartsolo-tree .ant-tree-title {
  /* 确保标题区域稳定 */
  min-height: 40px;
  line-height: 40px;
  
  /* 防止文本变化导致的布局抖动 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 修复可能的 z-index 问题 */
.smartsolo-tree .ant-tree-treenode:hover {
  /* 确保 hover 状态不会影响其他元素 */
  z-index: auto;
  position: relative;
}

/* 防止选中状态的抖动 */
.smartsolo-tree .ant-tree-treenode-selected {
  /* 确保选中状态稳定 */
  z-index: 1;
}

/* 针对可能的浏览器兼容性问题 */
@supports (backdrop-filter: blur(0)) {
  .smartsolo-tree .ant-tree-treenode {
    /* 现代浏览器的额外优化 */
    contain: layout style paint;
  }
}

/* 移动端特殊处理 */
@media (hover: none) {
  .smartsolo-tree .ant-tree-treenode:hover {
    /* 在触摸设备上禁用 hover 效果 */
    background-color: transparent !important;
  }
}

/* 高 DPI 屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .smartsolo-tree .ant-tree-treenode {
    /* 高分辨率屏幕的额外优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 针对树节点内的交互元素防抖动 */
.smartsolo-tree .ant-tree-title .iconfont {
  /* 防止图标 hover 导致的抖动 */
  transition: opacity 0.2s ease-in-out;
  pointer-events: auto;

  /* 确保图标区域稳定 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
}

/* 防止拖拽图标导致的抖动 */
.smartsolo-tree .tree-video-title {
  /* 确保标题区域稳定 */
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 防止删除按钮等操作按钮导致的抖动 */
.smartsolo-tree .ant-tree-title .d-flex {
  /* 确保按钮容器稳定 */
  align-items: center;
  gap: 8px;
}

.smartsolo-tree .ant-tree-title .d-flex .iconfont {
  /* 防止操作按钮的 hover 影响整个节点 */
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

/* 特殊处理：防止 Tooltip 导致的抖动 */
.smartsolo-tree .ant-tooltip {
  pointer-events: none;
}

.smartsolo-tree .ant-tooltip-inner {
  pointer-events: none;
}
