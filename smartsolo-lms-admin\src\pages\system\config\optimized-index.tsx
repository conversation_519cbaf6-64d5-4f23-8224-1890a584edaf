import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Row,
  Form,
  Input,
  Button,
  Tabs,
  message,
  Switch,
  Checkbox,
  Slider,
  Space,
} from "antd";
import { appConfig, system } from "../../../api/index";
import { UploadImageButton } from "../../../compenents";
import { useDispatch } from "react-redux";
import type { TabsProps } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  SystemConfigStoreInterface,
  saveConfigAction,
} from "../../../store/system/systemConfigSlice";
import logoIcon from "../../../assets/logo.png";
import memberDefaultAvatar from "../../../assets/thumb/avatar.png";
import { ImageUploadField, FormSection, SwitchField } from "./components";

// 配置项常量定义
const CONFIG_KEYS = {
  SYSTEM_NAME: "system.name",
  SYSTEM_LOGO: "system.logo",
  SYSTEM_PC_URL: "system.pc_url",
  SYSTEM_H5_URL: "system.h5_url",
  SYSTEM_FOOTER: "system.pc_index_footer_msg",
  PLAYER_POSTER: "player.poster",
  PLAYER_DISABLED_DRAG: "player.disabled_drag",
  PLAYER_BULLET_SECRET: "player.is_enabled_bullet_secret",
  PLAYER_BULLET_TEXT: "player.bullet_secret_text",
  PLAYER_BULLET_COLOR: "player.bullet_secret_color",
  PLAYER_BULLET_OPACITY: "player.bullet_secret_opacity",
  MEMBER_AVATAR: "member.default_avatar",
  S3_ACCESS_KEY: "s3.access_key",
  S3_SECRET_KEY: "s3.secret_key",
  S3_BUCKET: "s3.bucket",
  S3_REGION: "s3.region",
  S3_ENDPOINT: "s3.endpoint",
  LDAP_ENABLED: "ldap.enabled",
  LDAP_URL: "ldap.url",
  LDAP_ADMIN_USER: "ldap.admin_user",
  LDAP_ADMIN_PASS: "ldap.admin_pass",
  LDAP_BASE_DN: "ldap.base_dn",
} as const;

// 类型定义
interface ConfigData {
  key_name: string;
  key_value: string;
}

interface BulletSecretState {
  name: boolean;
  email: boolean;
  idCard: boolean;
}

const SystemConfigPage: React.FC = () => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [images, setImages] = useState({
    logo: "",
    thumb: "",
    avatar: "",
  });
  const [resourceUrl, setResourceUrl] = useState<ResourceUrlModel>({});
  const [bulletSecretChecked, setBulletSecretChecked] = useState<BulletSecretState>({
    name: false,
    email: false,
    idCard: false,
  });

  // 配置数据处理器
  const configProcessors = useMemo(() => ({
    [CONFIG_KEYS.SYSTEM_NAME]: (value: string) => ({ "system.name": value }),
    [CONFIG_KEYS.SYSTEM_LOGO]: (value: string, resourceUrl: ResourceUrlModel) => {
      const logoUrl = value ? resourceUrl[Number(value)] : logoIcon;
      setImages(prev => ({ ...prev, logo: logoUrl }));
      return { "system.logo": value };
    },
    [CONFIG_KEYS.SYSTEM_PC_URL]: (value: string) => ({ "system.pc_url": value }),
    [CONFIG_KEYS.SYSTEM_H5_URL]: (value: string) => ({ "system.h5_url": value }),
    [CONFIG_KEYS.SYSTEM_FOOTER]: (value: string) => ({ "system.pc_index_footer_msg": value }),
    [CONFIG_KEYS.PLAYER_POSTER]: (value: string, resourceUrl: ResourceUrlModel) => {
      if (value) {
        setImages(prev => ({ ...prev, thumb: resourceUrl[Number(value)] }));
      }
      return { "player.poster": value };
    },
    [CONFIG_KEYS.PLAYER_DISABLED_DRAG]: (value: string) => ({
      "player.disabled_drag": value === "1" ? 1 : 0
    }),
    [CONFIG_KEYS.PLAYER_BULLET_SECRET]: (value: string) => ({
      "player.is_enabled_bullet_secret": value === "1" ? 1 : 0
    }),
    [CONFIG_KEYS.PLAYER_BULLET_TEXT]: (value: string) => {
      setBulletSecretChecked({
        name: value.includes("{name}"),
        email: value.includes("{email}"),
        idCard: value.includes("{idCard}"),
      });
      return { "player.bullet_secret_text": value };
    },
    [CONFIG_KEYS.PLAYER_BULLET_COLOR]: (value: string) => ({ "player.bullet_secret_color": value }),
    [CONFIG_KEYS.PLAYER_BULLET_OPACITY]: (value: string) => ({
      "player.bullet_secret_opacity": value ? Number(value) * 100 : 0
    }),
    [CONFIG_KEYS.MEMBER_AVATAR]: (value: string, resourceUrl: ResourceUrlModel) => {
      const avatarUrl = value ? resourceUrl[Number(value)] : memberDefaultAvatar;
      setImages(prev => ({ ...prev, avatar: avatarUrl }));
      return { "member.default_avatar": value };
    },
    // S3 配置
    [CONFIG_KEYS.S3_ACCESS_KEY]: (value: string) => ({ "s3.access_key": value }),
    [CONFIG_KEYS.S3_SECRET_KEY]: (value: string) => ({ "s3.secret_key": value }),
    [CONFIG_KEYS.S3_BUCKET]: (value: string) => ({ "s3.bucket": value }),
    [CONFIG_KEYS.S3_REGION]: (value: string) => ({ "s3.region": value }),
    [CONFIG_KEYS.S3_ENDPOINT]: (value: string) => ({ "s3.endpoint": value }),
    // LDAP 配置
    [CONFIG_KEYS.LDAP_ENABLED]: (value: string) => ({ "ldap.enabled": value === "1" ? 1 : 0 }),
    [CONFIG_KEYS.LDAP_URL]: (value: string) => ({ "ldap.url": value }),
    [CONFIG_KEYS.LDAP_ADMIN_USER]: (value: string) => ({ "ldap.admin_user": value }),
    [CONFIG_KEYS.LDAP_ADMIN_PASS]: (value: string) => ({ "ldap.admin_pass": value }),
    [CONFIG_KEYS.LDAP_BASE_DN]: (value: string) => ({ "ldap.base_dn": value }),
  }), []);

  // 获取配置详情
  const getDetail = useCallback(async () => {
    try {
      const res = await appConfig.appConfig();
      setResourceUrl(res.data.resource_url);

      const configData: ConfigData[] = res.data.app_config;
      const formValues: Record<string, any> = {};

      configData.forEach(config => {
        const processor = configProcessors[config.key_name as keyof typeof configProcessors];
        if (processor) {
          const result = processor(config.key_value, res.data.resource_url);
          Object.assign(formValues, result);
        }
      });

      form.setFieldsValue(formValues);
    } catch (error) {
      message.error("获取配置失败");
      console.error("获取配置失败:", error);
    }
  }, [form, configProcessors]);

  useEffect(() => {
    getDetail();
  }, [getDetail]);

  // 切换处理器
  const createSwitchHandler = useCallback((fieldName: string) => (checked: boolean) => {
    form.setFieldsValue({ [fieldName]: checked ? 1 : 0 });
  }, [form]);

  // 跑马灯文本处理器
  const createBulletTextHandler = useCallback((placeholder: string, key: keyof BulletSecretState) =>
    (e: CheckboxChangeEvent) => {
      const currentValue = form.getFieldValue(CONFIG_KEYS.PLAYER_BULLET_TEXT) || "";
      const newValue = e.target.checked
        ? currentValue + placeholder
        : currentValue.replace(placeholder, "");

      form.setFieldsValue({ [CONFIG_KEYS.PLAYER_BULLET_TEXT]: newValue });
      setBulletSecretChecked(prev => ({ ...prev, [key]: !prev[key] }));
    }, [form]);

  // 表单提交
  const onFinish = useCallback(async (values: any) => {
    if (loading) return;

    setLoading(true);
    try {
      // 处理透明度值
      if (values["player.bullet_secret_opacity"]) {
        values["player.bullet_secret_opacity"] = values["player.bullet_secret_opacity"] / 100;
      }

      await appConfig.saveAppConfig(values);
      message.success("保存成功！");
      await getDetail();
      await getSystemConfig();
    } catch (error) {
      message.error("保存失败");
      console.error("保存失败:", error);
    } finally {
      setLoading(false);
    }
  }, [loading, getDetail]);

  // 获取系统配置
  const getSystemConfig = useCallback(async () => {
    try {
      const res = await system.getSystemConfig();
      const data: SystemConfigStoreInterface = {
        "ldap-enabled": res.data["ldap-enabled"],
        systemName: res.data["system.name"],
        systemLogo: res.data["system.logo"],
        systemPcUrl: res.data["system.pc_url"],
        systemH5Url: res.data["system.h5_url"],
        memberDefaultAvatar: res.data["member.default_avatar"],
        courseDefaultThumbs: res.data["default.course_thumbs"],
        departments: res.data["departments"],
        resourceCategories: res.data["resource_categories"],
      };
      dispatch(saveConfigAction(data));
    } catch (error) {
      console.error("获取系统配置失败:", error);
    }
  }, [dispatch]);

  const onFinishFailed = useCallback((errorInfo: any) => {
    console.log("Failed:", errorInfo);
  }, []);

  // 创建处理器实例
  const onDragChange = createSwitchHandler(CONFIG_KEYS.PLAYER_DISABLED_DRAG);
  const onSwitchChange = createSwitchHandler(CONFIG_KEYS.PLAYER_BULLET_SECRET);
  const onLDAPChange = createSwitchHandler(CONFIG_KEYS.LDAP_ENABLED);

  const addName = createBulletTextHandler("{name}", "name");
  const addEmail = createBulletTextHandler("{email}", "email");
  const addIdCard = createBulletTextHandler("{idCard}", "idCard");

  // Tab 配置
  const tabItems: TabsProps["items"] = useMemo(() => [
    {
      key: "1",
      label: "网站设置",
      children: (
        <FormSection
          name="website-config"
          loading={loading}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        >
          <ImageUploadField
            label="PC学员端Logo"
            name={CONFIG_KEYS.SYSTEM_LOGO}
            imageUrl={images.logo}
            buttonText="更换Logo"
            helpText="（推荐尺寸:240x80px，支持JPG、PNG）"
            onImageSelected={(url, id) => {
              setImages(prev => ({ ...prev, logo: url }));
              form.setFieldsValue({ [CONFIG_KEYS.SYSTEM_LOGO]: id });
            }}
            imageStyle={{ height: 40 }}
            labelStyle={{ marginTop: 4, marginLeft: 24 }}
          />

          <Form.Item
            style={{ marginBottom: 30 }}
            label="PC学员端地址"
            name={CONFIG_KEYS.SYSTEM_PC_URL}
          >
            <Input style={{ width: 274 }} placeholder="请填写PC学员端地址" />
          </Form.Item>

          <Form.Item
            style={{ marginBottom: 30 }}
            label="H5学员端地址"
            name={CONFIG_KEYS.SYSTEM_H5_URL}
          >
            <Input style={{ width: 274 }} placeholder="请填写H5学员端地址" />
          </Form.Item>

          <Form.Item
            style={{ marginBottom: 30 }}
            label="学员端标题"
            name={CONFIG_KEYS.SYSTEM_NAME}
          >
            <Input
              style={{ width: 274 }}
              allowClear
              placeholder="请填写学员端标题"
            />
          </Form.Item>

          <Form.Item
            style={{ marginBottom: 30 }}
            label="学员端页脚"
            name={CONFIG_KEYS.SYSTEM_FOOTER}
          >
            <Input
              style={{ width: 274 }}
              allowClear
              placeholder="请填写学员端页脚"
            />
          </Form.Item>
        </FormSection>
      ),
    },
    {
      key: "2",
      label: "播放设置",
      children: (
        <FormSection
          name="player-config"
          loading={loading}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        >
          <SwitchField
            label="禁止拖动进度条"
            name={CONFIG_KEYS.PLAYER_DISABLED_DRAG}
            helpText="（打开后禁止学员在首次学习中拖动进度条，以防刷课）"
            onChange={onDragChange}
          />

          <SwitchField
            label="播放器跑马灯"
            name={CONFIG_KEYS.PLAYER_BULLET_SECRET}
            helpText="（打开后播放器会随机出现跑马灯水印，以防录屏传播）"
            onChange={onSwitchChange}
          />

          <Form.Item style={{ marginBottom: 30 }} label="跑马灯内容">
            <Space align="baseline" style={{ height: 32 }}>
              <Form.Item name={CONFIG_KEYS.PLAYER_BULLET_TEXT}>
                <Input
                  style={{ width: 274 }}
                  allowClear
                  placeholder="自定义跑马灯内容"
                />
              </Form.Item>
              <Checkbox
                checked={bulletSecretChecked.name}
                className="ml-24"
                onChange={addName}
              >
                姓名
              </Checkbox>
              <Checkbox
                checked={bulletSecretChecked.email}
                className="ml-24"
                onChange={addEmail}
              >
                邮箱
              </Checkbox>
              <Checkbox
                checked={bulletSecretChecked.idCard}
                className="ml-24"
                onChange={addIdCard}
              >
                身份证
              </Checkbox>
            </Space>
          </Form.Item>

          <Form.Item
            style={{ marginBottom: 30 }}
            label="跑马灯文字颜色"
            name={CONFIG_KEYS.PLAYER_BULLET_COLOR}
          >
            <Input type="color" style={{ width: 32, padding: 0 }} />
          </Form.Item>

          <Form.Item
            style={{ marginBottom: 30 }}
            label="跑马灯不透明度"
            name={CONFIG_KEYS.PLAYER_BULLET_OPACITY}
          >
            <Slider style={{ width: 400 }} range defaultValue={[0, 100]} />
          </Form.Item>

          <ImageUploadField
            label="播放器封面"
            name={CONFIG_KEYS.PLAYER_POSTER}
            imageUrl={images.thumb}
            buttonText="更换封面"
            helpText="（推荐尺寸:1920x1080px，视频播放未开始时展示）"
            onImageSelected={(url, id) => {
              setImages(prev => ({ ...prev, thumb: url }));
              form.setFieldsValue({ [CONFIG_KEYS.PLAYER_POSTER]: id });
            }}
            imageStyle={{ height: 180, borderRadius: 6 }}
            labelStyle={{ marginTop: 75, marginLeft: 42 }}
          />
        </FormSection>
      ),
    },
    // 其他 tabs 配置...
  ], [
    loading,
    onFinish,
    onFinishFailed,
    images,
    form,
    bulletSecretChecked,
    onDragChange,
    onSwitchChange,
    addName,
    addEmail,
    addIdCard,
  ]);

  return (
    <Row className="smartsolo-main-body">
      <Tabs
        className="float-left"
        defaultActiveKey="1"
        items={tabItems}
      />
    </Row>
  );
};

export default SystemConfigPage;
