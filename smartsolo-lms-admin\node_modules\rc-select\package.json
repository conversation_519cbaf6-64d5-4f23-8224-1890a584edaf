{"name": "rc-select", "version": "14.16.8", "description": "React Select", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-select", "select"], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "files": ["assets/*.css", "assets/*.less", "es", "lib"], "homepage": "http://github.com/react-component/select", "repository": {"type": "git", "url": "**************:react-component/select.git"}, "bugs": {"url": "http://github.com/react-component/select/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "build": "dumi build", "prepare": "husky && dumi setup", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch antd-5.x", "prettier": "prettier --write --ignore-unknown .", "lint": "eslint src/ docs/ tests/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "tsc": "tsc --noEmit", "now-build": "npm run build"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.1.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-overflow": "^1.3.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.2"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.6", "@types/jest": "^29.5.12", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "babel-jest": "^29.6.1", "cross-env": "^7.0.0", "dumi": "^2.2.13", "eslint": "^8.55.0", "eslint-plugin-jest": "^28.8.1", "eslint-plugin-unicorn": "^56.0.0", "father": "^4.0.0", "husky": "^9.1.5", "jsonp": "^0.2.1", "less": "^4.2.0", "lint-staged": "^15.2.9", "np": "^10.0.7", "prettier": "^3.1.1", "querystring": "^0.2.1", "rc-dialog": "^9.0.0", "rc-test": "^7.0.9", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2"}}