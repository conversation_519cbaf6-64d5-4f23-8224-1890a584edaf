import React from 'react';
import { Form, Switch, Space } from 'antd';

interface SwitchFieldProps {
  label: string;
  name: string;
  helpText?: string;
  onChange: (checked: boolean) => void;
}

const SwitchField: React.FC<SwitchFieldProps> = ({
  label,
  name,
  helpText,
  onChange,
}) => {
  return (
    <Form.Item style={{ marginBottom: 30 }} label={label}>
      <Space align="baseline" style={{ height: 32 }}>
        <Form.Item name={name} valuePropName="checked">
          <Switch onChange={onChange} />
        </Form.Item>
        {helpText && (
          <div className="helper-text">
            {helpText}
          </div>
        )}
      </Space>
    </Form.Item>
  );
};

export default SwitchField;
