import React, { useEffect, useState } from "react";
import { Menu } from "antd";
import { useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import styles from "./index.module.less";
import logo from "@/assets/Frame 427320293.png";
import Group3 from "@/assets/images/leftmenu/Group 3.png";
import Group4 from "@/assets/images/leftmenu/Group 4.png";
import Vector from "@/assets/images/leftmenu/Vector.png";
import Vector1 from "@/assets/images/leftmenu/Vector-1.png";
import Frame427320479 from "@/assets/images/leftmenu/Frame 427320479.png";
import Frame427320483 from "@/assets/images/leftmenu/Frame 427320483.png";
import Frame427320480 from "@/assets/images/leftmenu/Frame 427320480.png";
import Frame427320484 from "@/assets/images/leftmenu/Frame 427320484.png";
import Frame427320481 from "@/assets/images/leftmenu/Frame 427320481.png";
import Frame427320485 from "@/assets/images/leftmenu/Frame 427320485.png";
import Frame427320482 from "@/assets/images/leftmenu/Frame 427320482.png";
import Frame427320486 from "@/assets/images/leftmenu/Frame 427320486.png";

function getItem(
  label: any,
  key: any,
  icon: any,
  children: any,
  type: any,
  permission: any,
  selectedIcon?: any
) {
  return {
    key,
    icon,
    selectedIcon,
    children,
    label,
    type,
    permission,
  };
}
const items = [
  getItem(
    "首页概览",
    "/",
    Group4,
    null,
    null,
    null,
    Group3
  ),
  getItem(
    "分类管理",
    "/resource-category",
    Vector1,
    null,
    null,
    "resource-category-menu",
    Vector
  ),
  getItem(
    "资源管理",
    "resource",
    Frame427320483,
    [
      getItem("视频", "/videos", null, null, null, "resource-menu"),
      getItem("图片", "/images", null, null, null, "resource-menu"),
      getItem("课件", "/courseware", null, null, null, "resource-menu"),
    ],
    null,
    null,
    Frame427320479
  ),
  getItem(
    "课程中心",
    "courses",
    Frame427320484,
    [getItem("线上课", "/course", null, null, null, "course")],
    null,
    null,
    Frame427320480
  ),
  getItem(
    "学员管理",
    "user",
    Frame427320485,
    [
      getItem("学员", "/member/index", null, null, null, "user-index"),
      getItem("部门", "/department", null, null, null, "department-cud"),
    ],
    null,
    null,
    Frame427320481
  ),
  getItem(
    "系统设置",
    "system",
    Frame427320486,
    [
      getItem(
        "系统配置",
        "/system/config/index",
        null,
        null,
        null,
        "system-config"
      ),
      getItem(
        "管理人员",
        "/system/administrator",
        null,
        null,
        null,
        "admin-user-index"
      ),
      getItem("管理日志", "/system/adminlog", null, null, null, "admin-log"),
    ],
    null,
    null,
    Frame427320482
  ),
];

export const LeftMenu: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const children2Parent: any = {
    "^/video": ["resource"],
    "^/image": ["resource"],
    "^/courseware": ["resource"],
    "^/member": ["user"],
    "^/department": ["user"],
    "^/course": ["courses"],
    "^/system": ["system"],
  };

  const hit = (pathname: string): string[] => {
    for (let p in children2Parent) {
      if (pathname.search(p) >= 0) {
        return children2Parent[p];
      }
    }
    return [];
  };

  const openKeyMerge = (pathname: string): string[] => {
    let newOpenKeys = hit(pathname);
    for (let i = 0; i < openKeys.length; i++) {
      let isIn = false;
      for (let j = 0; j < newOpenKeys.length; j++) {
        if (newOpenKeys[j] === openKeys[i]) {
          isIn = true;
          break;
        }
      }
      if (isIn) {
        continue;
      }
      newOpenKeys.push(openKeys[i]);
    }
    return newOpenKeys;
  };

  // 选中的菜单
  const [selectedKeys, setSelectedKeys] = useState<string[]>([
    location.pathname,
  ]);
  // 展开菜单
  const [openKeys, setOpenKeys] = useState<string[]>(hit(location.pathname));
  const permissions = useSelector(
    (state: any) => state.loginUser.value.permissions
  );
  const [activeMenus, setActiveMenus] = useState<any>([]);

  const onClick = (e: any) => {
    navigate(e.key);
  };

  useEffect(() => {
    checkMenuPermissions(items, permissions);
  }, [items, permissions]);

  const checkMenuPermissions = (items: any, permissions: any) => {
    let menus: any = [];
    if (permissions.length === 0) {
      setActiveMenus(menus);
      return;
    }

    for (let i in items) {
      let menuItem = items[i];
      // 一级菜单=>没有子菜单&配置了权限
      if (menuItem.children === null) {
        if (
          menuItem.permission !== null &&
          typeof permissions[menuItem.permission] === "undefined"
        ) {
          continue;
        }
        menus.push(menuItem);
        continue;
      }
      let children = [];

      for (let j in menuItem.children) {
        let childrenItem = menuItem.children[j];

        if (
          typeof permissions[childrenItem.permission] !== "undefined" ||
          !childrenItem.permission
        ) {
          // 存在权限
          children.push(childrenItem);
        }
      }

      if (children.length > 0) {
        menus.push(Object.assign({}, menuItem, { children: children }));
      }
    }
    setActiveMenus(menus);
  };

  useEffect(() => {
    if (location.pathname.indexOf("/course/user") !== -1) {
      setSelectedKeys(["/course"]);
      setOpenKeys(openKeyMerge("/course"));
    } else if (location.pathname.indexOf("/member/learn") !== -1) {
      setSelectedKeys(["/member/index"]);
      setOpenKeys(openKeyMerge("/member/index"));
    } else {
      setSelectedKeys([location.pathname]);
      setOpenKeys(openKeyMerge(location.pathname));
    }
  }, [location.pathname]);

  // 在 LeftMenu 组件中添加处理图标的方法
  const processMenuItems = (items: any[]): any[] => {
    return items.map(item => {
      // 创建新的菜单项对象，排除自定义属性
      const { selectedIcon, ...restItem } = item;

      // 处理图标显示逻辑
      let icon = item.icon;
      if (item.selectedIcon && selectedKeys.includes(item.key)) {
        icon = typeof item.selectedIcon === 'string'
          ? <img src={item.selectedIcon} style={{ width: 16, height: 16 }} />
          : item.selectedIcon;
      } else if (typeof item.icon === 'string') {
        icon = <img src={item.icon} style={{ width: 16, height: 16 }} />;
      }

      // 处理子菜单项
      if (item.children) {
        return {
          ...restItem,
          icon,
          children: processMenuItems(item.children)
        };
      }

      return {
        ...restItem,
        icon
      };
    });
  };

  return (
    <div className={styles["left-menu"]}>
      <div
        style={{
          textDecoration: "none",
          cursor: "pointer",
          position: "sticky",
          top: 0,
          height: 48,
          zIndex: 10,
          background: "#66B821",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
        onClick={() => {
          window.location.href = "/";
        }}
      >
        {/* 此处为版权标识，严禁删改 */}
        <img src={logo} className={styles["App-logo"]} />
      </div>
      <div className={styles["menu-box"]}>
        <Menu
          onClick={onClick}
          style={{
            width: 200,
            background: "#1B2534",
          }}
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          mode="inline"
          theme="light"
          items={processMenuItems(activeMenus)}
          onSelect={(data: any) => {
            setSelectedKeys(data.selectedKeys);
          }}
          onOpenChange={(keys: any) => {
            setOpenKeys(keys);
          }}
        />
      </div>
    </div>
  );
};
