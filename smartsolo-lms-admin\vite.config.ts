import { defineConfig, loadEnv } from "vite";
import path from 'path';
import react from "@vitejs/plugin-react-swc";
// import gzipPlugin from "rollup-plugin-gzip";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    server: {
      host: "0.0.0.0",
      port: 9393,
      proxy: {
        // 选项写法
        '/api': {
          target: process.env.VITE_API_BASE_PATH,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
    },
    plugins: [react()],
    // build: {
    //   rollupOptions: {
    //     plugins: [gzipPlugin()],
    //   },
    // },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    build: {
      outDir: env.VITE_OUT_DIR || 'dist',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: env.VITE_DISABLE_LOG === 'true',
          drop_debugger: true,
        },
      },
    },
  }
});

