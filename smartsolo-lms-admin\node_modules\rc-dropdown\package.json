{"name": "rc-dropdown", "version": "4.2.1", "description": "dropdown ui component for react", "keywords": ["react", "react-dropdown"], "homepage": "http://github.com/react-component/dropdown", "bugs": {"url": "http://github.com/react-component/dropdown/issues"}, "repository": {"type": "git", "url": "**************:react-component/dropdown.git"}, "license": "MIT", "maintainers": ["<EMAIL>", "<EMAIL>"], "main": "lib/index", "module": "./es/index", "files": ["lib", "es", "assets/*.css"], "scripts": {"build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepare": "husky install && dumi setup", "prepublishOnly": "npm run compile", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.18.3", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-util": "^5.44.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/classnames": "^2.2.6", "@types/jest": "^29.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^2.0.0", "eslint": "^7.18.0", "father": "^4.0.0", "glob": "^10.0.0", "husky": "^8.0.3", "jest-environment-jsdom": "^29.5.0", "jquery": "^3.3.1", "less": "^4.1.1", "lint-staged": "^13.2.1", "np": "^6.0.0", "prettier": "^2.8.7", "rc-menu": "^9.5.2", "rc-resize-observer": "^1.4.0", "rc-test": "^7.0.14", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.13.9", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}}