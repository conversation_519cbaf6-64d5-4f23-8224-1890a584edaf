{"name": "rc-field-form", "version": "2.7.0", "description": "React Form Component", "typings": "es/index.d.ts", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-form", "form"], "homepage": "https://github.com/react-component/field-form", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/field-form.git"}, "bugs": {"url": "https://github.com/react-component/field-form/issues"}, "files": ["lib", "es", "dist", "assets/*.css"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father build", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "rc-test", "test:coverage": "umi-test --coverage", "prepublishOnly": "npm run compile ", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/async-validator": "^5.0.3", "rc-util": "^5.32.2"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@types/jest": "^29.2.5", "@types/lodash": "^4.14.135", "@types/node": "^22.0.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^4.0.1", "dumi": "^2.0.0", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^56.0.1", "father": "^4.0.0", "gh-pages": "^6.1.0", "jest": "^29.0.0", "np": "^10.0.2", "prettier": "^3.1.0", "rc-test": "^7.0.15", "react": "^18.0.0", "react-dnd": "^8.0.3", "react-dnd-html5-backend": "^8.0.3", "react-dom": "^18.0.0", "react-redux": "^9.0.4", "redux": "^5.0.0", "typescript": "^5.1.6"}}