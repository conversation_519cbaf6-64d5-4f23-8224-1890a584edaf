{"name": "rc-pagination", "version": "5.1.0", "description": "pagination ui component for react", "keywords": ["react", "react-component", "react-pagination", "pagination", "antd", "pager"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib"], "homepage": "https://react-component.github.io/pagination", "repository": {"type": "git", "url": "**************:react-component/pagination.git"}, "bugs": {"url": "http://github.com/react-component/pagination/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "gh-pages": "npm run docs:build && npm run docs:deploy", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish && npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "rc-test", "coverage": "rc-test --coverage", "now-build": "npm run docs:build", "prepare": "husky"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.2.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^4.0.1", "coveralls": "^3.0.6", "cross-env": "^7.0.0", "dumi": "^2.1.2", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^55.0.0", "father": "^4.0.0", "gh-pages": "^6.1.0", "glob": "^10.3.10", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "less": "^4.1.3", "lint-staged": "^15.0.2", "np": "^10.0.5", "prettier": "^3.1.0", "rc-select": "^14.16.4", "rc-test": "^7.0.15", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write"]}}