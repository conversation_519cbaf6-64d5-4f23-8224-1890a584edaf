import React from 'react';
import { Form, Button } from 'antd';

interface FormSectionProps {
  name: string;
  loading: boolean;
  onFinish: (values: any) => void;
  onFinishFailed: (errorInfo: any) => void;
  children: React.ReactNode;
}

const FormSection: React.FC<FormSectionProps> = ({
  name,
  loading,
  onFinish,
  onFinishFailed,
  children,
}) => {
  return (
    <Form
      name={name}
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 21 }}
      style={{ width: 1000, paddingTop: 30 }}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
    >
      {children}
      <Form.Item
        style={{ marginBottom: 30 }}
        wrapperCol={{ offset: 3, span: 21 }}
      >
        <Button type="primary" htmlType="submit" loading={loading}>
          保存
        </Button>
      </Form.Item>
    </Form>
  );
};

export default FormSection;
